<template>
  <el-dialog
    v-model="visible"
    :title="config.title"
    :width="config.width"
    :draggable="true"
    class="detail-dialog"
  >
    <div class="dialog-content">
      <!-- 动态渲染内容 -->
      <component
        :is="currentComponent"
        :data="data"
        :config="config"
        @row-click="handleRowClick"
        @expand-change="handleExpandChange"
        :expanded-rows="expandedRows"
      />
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="visible = false">{{ config.closeText || '关闭' }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, defineAsyncComponent } from 'vue'

// 动态组件映射
const componentMap = {
  RuleDetailContent: defineAsyncComponent(() => import('./RuleDetailContent.vue')),
  ExecuteRecordContent: defineAsyncComponent(() => import('./ExecuteRecordContent.vue'))
}

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  },
  config: {
    type: Object,
    default: () => ({
      type: 'RuleDetailContent', // 内容组件类型
      title: '详情',
      width: '1400px',
      closeText: '关闭'
    })
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 展开的行
const expandedRows = ref([])

// 动态获取组件
const currentComponent = computed(() => {
  return componentMap[props.config.type] || componentMap.RuleDetailContent
})

/**
 * 处理行点击事件
 * @param {Object} row 点击的行数据
 */
const handleRowClick = (row) => {
  // 切换展开状态
  const index = expandedRows.value.indexOf(row.id)
  if (index > -1) {
    expandedRows.value.splice(index, 1)
  } else {
    expandedRows.value.push(row.id)
  }
}

/**
 * 处理展开变化事件
 * @param {Object} row 行数据
 * @param {Array} expandedRowsData 展开的行数据
 */
const handleExpandChange = (row, expandedRowsData) => {
  // 同步展开状态
  expandedRows.value = expandedRowsData.map(item => item.id)
}

// 监听visible变化，打开时重置状态
watch(visible, (newVal) => {
  if (newVal) {
    expandedRows.value = []
  }
})
</script>

<style lang="scss" scoped>
.detail-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
  }

  // 内容样式由子组件自己管理
}
</style>
