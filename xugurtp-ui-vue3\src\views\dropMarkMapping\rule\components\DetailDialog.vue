<template>
  <el-dialog
    v-model="visible"
    title="落标明细"
    width="1400px"
    :draggable="true"
    class="detail-dialog"
  >
    <div class="dialog-content">
      <!-- 基础信息 -->
      <div class="basic-info">
        <div class="info-row">
          <div class="info-item">
            <span class="label">规则名称：</span>
            <span class="value">{{ basicInfo.ruleName }}</span>
          </div>
          <div class="info-item">
            <span class="label">映射数据集/对象：</span>
            <span class="value">{{ basicInfo.mappingDataset }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="label">开始执行时间：</span>
            <span class="value">{{ basicInfo.startTime }}</span>
          </div>
          <div class="info-item">
            <span class="label">有效映射关系数：</span>
            <span class="value">{{ basicInfo.validMappingCount }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="label">执行结果：</span>
            <span class="value">
              {{ basicInfo.executeResult }}
              <el-tag
                :type="basicInfo.executeStatus === '成功' ? 'success' : 'danger'"
                size="small"
                style="margin-left: 8px"
              >
                {{ basicInfo.executeStatus }}
              </el-tag>
            </span>
          </div>
        </div>
      </div>

      <!-- 折叠面板 -->
      <el-collapse v-model="activeCollapse" class="detail-collapse">
        <el-collapse-item title="标集对照" name="standardComparison">
          <el-table
            :data="treeTableData"
            height="350px"
            :header-cell-class-name="addHeaderCellClassName"
            empty-text="暂无数据"
            row-key="id"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          >
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column prop="standardChineseName" label="标集中文名称" min-width="120" />
            <el-table-column prop="standardCode" label="标准编码" min-width="120" />
            <el-table-column prop="validMappingCount" label="有效映射关系数" min-width="140" />
            <el-table-column prop="mappingMetadataName" label="映射元数据名称" min-width="140" />
            <el-table-column prop="mappingMetadataCode" label="映射元数据编码" min-width="140" />
            <el-table-column prop="metadataPath" label="元数据路径" min-width="150" />
          </el-table>
        </el-collapse-item>

        <el-collapse-item title="资产对量对照" name="assetComparison">
          <div class="asset-comparison-content">
            <p class="description">
              标准集名称（目录）：{{ assetInfo.standardSetName }}
            </p>
            <p class="description">
              基础数据集（{{ assetInfo.datasetCategory }}）：{{ assetInfo.datasetName }}
            </p>
            <p class="description">
              点击右侧报告可访问数据集/微数据页面
            </p>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="visible = false">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const activeCollapse = ref(['standardComparison'])

const basicInfo = reactive({
  ruleName: '',
  mappingDataset: '',
  startTime: '',
  validMappingCount: 0,
  executeResult: '',
  executeStatus: ''
})

const assetInfo = reactive({
  standardSetName: '',
  datasetCategory: '',
  datasetName: ''
})

// 模拟树形标集对照数据
const treeTableData = ref([
  {
    id: 1,
    standardChineseName: '员工基础信息标准',
    standardCode: 'EMPLOYEE_BASE',
    validMappingCount: 15,
    mappingMetadataName: '员工基础信息',
    mappingMetadataCode: 'employee_base',
    metadataPath: '111/222/员工管理',
    hasChildren: true,
    children: [
      {
        id: 11,
        standardChineseName: '员工ID',
        standardCode: 'USERID',
        validMappingCount: 4,
        mappingMetadataName: '员工ID',
        mappingMetadataCode: 'user_id',
        metadataPath: '111/222/高名'
      },
      {
        id: 12,
        standardChineseName: '性别',
        standardCode: 'GENDER',
        validMappingCount: 3,
        mappingMetadataName: '性别',
        mappingMetadataCode: 'gender',
        metadataPath: '111/222/333'
      },
      {
        id: 13,
        standardChineseName: '员工姓名',
        standardCode: 'EMP_NAME',
        validMappingCount: 2,
        mappingMetadataName: '员工姓名',
        mappingMetadataCode: 'emp_name',
        metadataPath: '111/222/姓名'
      }
    ]
  },
  {
    id: 2,
    standardChineseName: '部门信息标准',
    standardCode: 'DEPARTMENT',
    validMappingCount: 8,
    mappingMetadataName: '部门信息',
    mappingMetadataCode: 'department',
    metadataPath: '111/222/部门管理',
    hasChildren: true,
    children: [
      {
        id: 21,
        standardChineseName: '部门ID',
        standardCode: 'DEPT_ID',
        validMappingCount: 5,
        mappingMetadataName: '部门ID',
        mappingMetadataCode: 'dept_id',
        metadataPath: '111/222/部门'
      },
      {
        id: 22,
        standardChineseName: '部门名称',
        standardCode: 'DEPT_NAME',
        validMappingCount: 3,
        mappingMetadataName: '部门名称',
        mappingMetadataCode: 'dept_name',
        metadataPath: '111/222/部门名称'
      }
    ]
  }
])

// 方法
const addHeaderCellClassName = () => 'table-header-cell'

// 监听数据变化
watch(() => props.data, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    // 设置基础信息
    Object.assign(basicInfo, {
      ruleName: '金融标准映射规则',
      mappingDataset: '落标数据集（公示标准版/基础）：性别_姓名_同工同',
      startTime: newData.startTime,
      validMappingCount: newData.validMappingCount,
      executeResult: newData.executeResult,
      executeStatus: newData.executeStatus
    })
    
    // 设置资产信息
    Object.assign(assetInfo, {
      standardSetName: '标准名称',
      datasetCategory: '公示标准版/基础',
      datasetName: '性别_姓名.xl'
    })
  }
}, { immediate: true })

// 监听visible变化，打开时重置状态
watch(visible, (newVal) => {
  if (newVal) {
    activeCollapse.value = ['standardComparison']
  }
})
</script>

<style lang="scss" scoped>
.detail-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
  }
  
  .dialog-content {
    .basic-info {
      margin-bottom: 20px;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;
      
      .info-row {
        display: flex;
        margin-bottom: 12px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .info-item {
          flex: 1;
          display: flex;
          align-items: center;
          
          .label {
            font-weight: 600;
            color: #606266;
            margin-right: 8px;
            min-width: 120px;
          }
          
          .value {
            color: #303133;
            display: flex;
            align-items: center;
          }
        }
      }
    }
    
    .detail-collapse {
      .asset-comparison-content {
        padding: 20px;

        .description {
          margin: 8px 0;
          color: #606266;
          font-size: 14px;

          &:first-child {
            margin-top: 0;
          }

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

:deep(.el-table) {
  .table-header-cell {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 600;
  }
}
</style>
