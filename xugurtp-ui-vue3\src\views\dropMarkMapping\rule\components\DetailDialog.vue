<template>
  <el-dialog
    v-model="visible"
    title="落标明细"
    width="1400px"
    :draggable="true"
    class="detail-dialog"
  >
    <div class="dialog-content">
      <!-- 基础信息 -->
      <div class="basic-info">
        <div class="info-row">
          <div class="info-item">
            <span class="label">规则名称：</span>
            <span class="value">{{ basicInfo.ruleName }}</span>
          </div>
          <div class="info-item">
            <span class="label">映射数据集/对象：</span>
            <span class="value">{{ basicInfo.mappingDataset }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="label">开始执行时间：</span>
            <span class="value">{{ basicInfo.startTime }}</span>
          </div>
          <div class="info-item">
            <span class="label">有效映射关系数：</span>
            <span class="value">{{ basicInfo.validMappingCount }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="label">执行结果：</span>
            <span class="value">
              {{ basicInfo.executeResult }}
              <el-tag
                :type="basicInfo.executeStatus === '成功' ? 'success' : 'danger'"
                size="small"
                style="margin-left: 8px"
              >
                {{ basicInfo.executeStatus }}
              </el-tag>
            </span>
          </div>
        </div>
      </div>

      <!-- 折叠面板 -->
      <el-collapse v-model="activeCollapse" class="detail-collapse">
        <el-collapse-item title="标集对照" name="standardComparison">
          <el-table
            :data="treeTableData"
            height="350px"
            :header-cell-class-name="addHeaderCellClassName"
            empty-text="暂无数据"
            row-key="id"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            @row-click="handleRowClick"
          >
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column prop="standardChineseName" label="标集中文名称" min-width="120" />
            <el-table-column prop="standardCode" label="标准编码" min-width="120" />
            <el-table-column prop="validMappingCount" label="有效映射关系数" min-width="140" />
            <el-table-column prop="mappingMetadataName" label="映射元数据名称" min-width="140" />
            <el-table-column prop="mappingMetadataCode" label="映射元数据编码" min-width="140" />
            <el-table-column prop="metadataPath" label="元数据路径" min-width="150" />
          </el-table>
        </el-collapse-item>

        <el-collapse-item title="资产对量对照" name="assetComparison">
          <div class="asset-comparison-content">
            <p class="description">
              标准集名称（目录）：{{ assetInfo.standardSetName }}
            </p>
            <p class="description">
              基础数据集（{{ assetInfo.datasetCategory }}）：{{ assetInfo.datasetName }}
            </p>
            <p class="description">
              点击右侧报告可访问数据集/微数据页面
            </p>
          </div>
        </el-collapse-item>
      </el-collapse>

      <!-- 标准字段详情 -->
      <div v-if="selectedStandard" class="standard-fields">
        <h4>{{ selectedStandard.standardChineseName }} - 标准字段</h4>
        <el-table
          :data="selectedStandard.fields"
          height="200px"
          :header-cell-class-name="addHeaderCellClassName"
          empty-text="暂无字段数据"
        >
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="fieldName" label="字段名称" min-width="120" />
          <el-table-column prop="fieldCode" label="字段编码" min-width="120" />
          <el-table-column prop="fieldType" label="字段类型" min-width="100" />
          <el-table-column prop="fieldLength" label="字段长度" min-width="100" />
          <el-table-column prop="isRequired" label="是否必填" min-width="100">
            <template #default="scope">
              <el-tag :type="scope.row.isRequired ? 'danger' : 'info'" size="small">
                {{ scope.row.isRequired ? '必填' : '非必填' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="字段描述" min-width="150" show-overflow-tooltip />
        </el-table>
      </div>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="visible = false">返回</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const activeCollapse = ref(['standardComparison'])
const selectedStandard = ref(null)

const basicInfo = reactive({
  ruleName: '',
  mappingDataset: '',
  startTime: '',
  validMappingCount: 0,
  executeResult: '',
  executeStatus: ''
})

const assetInfo = reactive({
  standardSetName: '',
  datasetCategory: '',
  datasetName: ''
})

// 模拟树形标集对照数据
const treeTableData = ref([
  {
    id: 1,
    standardChineseName: '员工基础信息标准',
    standardCode: 'EMPLOYEE_BASE',
    validMappingCount: 15,
    mappingMetadataName: '员工基础信息',
    mappingMetadataCode: 'employee_base',
    metadataPath: '111/222/员工管理',
    hasChildren: true,
    fields: [
      { fieldName: '员工ID', fieldCode: 'emp_id', fieldType: 'VARCHAR', fieldLength: '20', isRequired: true, description: '员工唯一标识' },
      { fieldName: '员工姓名', fieldCode: 'emp_name', fieldType: 'VARCHAR', fieldLength: '50', isRequired: true, description: '员工姓名' },
      { fieldName: '性别', fieldCode: 'gender', fieldType: 'CHAR', fieldLength: '1', isRequired: false, description: '员工性别' }
    ],
    children: [
      {
        id: 11,
        standardChineseName: '员工ID',
        standardCode: 'USERID',
        validMappingCount: 4,
        mappingMetadataName: '员工ID',
        mappingMetadataCode: 'user_id',
        metadataPath: '111/222/高名',
        fields: [
          { fieldName: '员工ID', fieldCode: 'emp_id', fieldType: 'VARCHAR', fieldLength: '20', isRequired: true, description: '员工唯一标识' }
        ]
      },
      {
        id: 12,
        standardChineseName: '性别',
        standardCode: 'GENDER',
        validMappingCount: 3,
        mappingMetadataName: '性别',
        mappingMetadataCode: 'gender',
        metadataPath: '111/222/333',
        fields: [
          { fieldName: '性别', fieldCode: 'gender', fieldType: 'CHAR', fieldLength: '1', isRequired: false, description: '员工性别' }
        ]
      }
    ]
  },
  {
    id: 2,
    standardChineseName: '部门信息标准',
    standardCode: 'DEPARTMENT',
    validMappingCount: 8,
    mappingMetadataName: '部门信息',
    mappingMetadataCode: 'department',
    metadataPath: '111/222/部门管理',
    hasChildren: true,
    fields: [
      { fieldName: '部门ID', fieldCode: 'dept_id', fieldType: 'VARCHAR', fieldLength: '10', isRequired: true, description: '部门唯一标识' },
      { fieldName: '部门名称', fieldCode: 'dept_name', fieldType: 'VARCHAR', fieldLength: '100', isRequired: true, description: '部门名称' }
    ],
    children: [
      {
        id: 21,
        standardChineseName: '部门ID',
        standardCode: 'DEPTID',
        validMappingCount: 5,
        mappingMetadataName: '部门ID',
        mappingMetadataCode: 'dept_id',
        metadataPath: '111/222/部门',
        fields: [
          { fieldName: '部门ID', fieldCode: 'dept_id', fieldType: 'VARCHAR', fieldLength: '10', isRequired: true, description: '部门唯一标识' }
        ]
      }
    ]
  }
])

// 方法
const addHeaderCellClassName = () => 'table-header-cell'

/**
 * 处理表格行点击事件
 * 点击标准行时显示对应的字段信息
 * @param {Object} row 点击的行数据
 */
const handleRowClick = (row) => {
  if (row.fields && row.fields.length > 0) {
    selectedStandard.value = row
  } else {
    selectedStandard.value = null
  }
}

// 监听数据变化
watch(() => props.data, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    // 设置基础信息
    Object.assign(basicInfo, {
      ruleName: '金融标准映射规则',
      mappingDataset: '落标数据集（公示标准版/基础）：性别_姓名_同工同',
      startTime: newData.startTime,
      validMappingCount: newData.validMappingCount,
      executeResult: newData.executeResult,
      executeStatus: newData.executeStatus
    })
    
    // 设置资产信息
    Object.assign(assetInfo, {
      standardSetName: '标准名称',
      datasetCategory: '公示标准版/基础',
      datasetName: '性别_姓名.xl'
    })
  }
}, { immediate: true })

// 监听visible变化，打开时重置状态
watch(visible, (newVal) => {
  if (newVal) {
    activeCollapse.value = ['standardComparison']
    selectedStandard.value = null
  }
})
</script>

<style lang="scss" scoped>
.detail-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
  }
  
  .dialog-content {
    .basic-info {
      margin-bottom: 20px;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;
      
      .info-row {
        display: flex;
        margin-bottom: 12px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .info-item {
          flex: 1;
          display: flex;
          align-items: center;
          
          .label {
            font-weight: 600;
            color: #606266;
            margin-right: 8px;
            min-width: 120px;
          }
          
          .value {
            color: #303133;
            display: flex;
            align-items: center;
          }
        }
      }
    }
    
    .detail-collapse {
      .asset-comparison-content {
        padding: 20px;

        .description {
          margin: 8px 0;
          color: #606266;
          font-size: 14px;

          &:first-child {
            margin-top: 0;
          }

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    .standard-fields {
      margin-top: 20px;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;

      h4 {
        margin: 0 0 16px 0;
        color: #409eff;
        font-size: 16px;
        font-weight: 600;
      }
    }
  }
}

:deep(.el-table) {
  .table-header-cell {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 600;
  }

  .el-table__row {
    cursor: pointer;

    &:hover {
      background-color: #f5f7fa;
    }
  }
}
</style>
