# DetailDialog 通用组件使用说明

DetailDialog 是一个通用的详情弹窗组件，支持动态内容渲染，可以在多个场景中复用。

## 功能特性

- **动态组件渲染**：支持根据配置动态加载不同的内容组件
- **行内折叠表格**：支持表格行点击展开详细信息
- **灵活配置**：通过配置对象控制弹窗标题、宽度、内容类型等
- **数据驱动**：通过数据对象传递显示内容

## 支持的内容类型

### 1. RuleDetailContent - 规则明细内容
用于显示评估规则的详细信息，包括规则名称、基础信息卡片和可展开的规则明细表格。

### 2. ExecuteRecordContent - 执行记录内容
用于显示落标执行记录的详细信息，包括规则信息、基础信息卡片和有效映射关系表格。

## 使用方法

### 1. 导入组件

```vue
<script setup>
import DetailDialog from '@/views/dropMarkMapping/rule/components/DetailDialog.vue'
</script>
```

### 2. 在模板中使用

```vue
<template>
  <!-- 规则明细弹窗 -->
  <DetailDialog
    v-model="ruleDetailDialog.visible"
    :data="ruleDetailDialogData"
    :config="ruleDetailDialogConfig"
  />
  
  <!-- 执行记录明细弹窗 -->
  <DetailDialog
    v-model="executeRecordDialog.visible"
    :data="executeRecordDialogData"
    :config="executeRecordDialogConfig"
  />
</template>
```

### 3. 配置数据结构

#### 规则明细配置示例

```javascript
// 弹窗状态
const ruleDetailDialog = reactive({
  visible: false
})

// 弹窗配置
const ruleDetailDialogConfig = reactive({
  type: 'RuleDetailContent',
  title: '规则明细',
  width: '1400px',
  closeText: '关闭'
})

// 弹窗数据
const ruleDetailDialogData = reactive({
  ruleName: '我是规则名称',
  basicInfo: {
    ruleName: '规则名称',
    dataStandard: '数据标准',
    standardSet: '基础标准集',
    mappingObject: 'dws_dddd_dddddd'
  },
  tableData: [
    {
      id: 1,
      standardAttribute: '员工ID',
      evaluationResult: '不通过',
      standardValue: 'STRING',
      objectValue: 'varchar(64)',
      ruleType: '元数据'
    }
  ]
})
```

#### 执行记录配置示例

```javascript
// 弹窗状态
const executeRecordDialog = reactive({
  visible: false
})

// 弹窗配置
const executeRecordDialogConfig = reactive({
  type: 'ExecuteRecordContent',
  title: '落标明细',
  width: '1400px',
  closeText: '关闭'
})

// 弹窗数据
const executeRecordDialogData = reactive({
  ruleName: '金融标准映射规则',
  basicInfo: {
    ruleName: '金融标准映射规则',
    mappingDataset: '落标数据集（公示标准版/基础）：性别_姓名_同工同',
    startTime: '2025-05-05 10:47:44',
    belongAsset: 'dws_dddd_dddddd'
  },
  tableData: [
    {
      id: 1,
      standardChineseName: '员工ID',
      standardCode: 'USERID',
      validMappingCount: 4,
      mappingRelations: [
        { metadataCode: 'user_id', metadataPath: '111/222/表名' }
      ]
    }
  ]
})
```

### 4. 打开弹窗

```javascript
const handleViewRuleDetail = (row) => {
  ruleDetailDialog.visible = true
  
  // 设置弹窗数据
  Object.assign(ruleDetailDialogData, {
    ruleName: '我是规则名称',
    basicInfo: {
      ruleName: '规则名称',
      dataStandard: '数据标准',
      standardSet: '基础标准集',
      mappingObject: row.belongAsset || 'dws_dddd_dddddd'
    },
    tableData: mockRuleDetailData
  })
}

const handleViewExecuteRecord = (row) => {
  executeRecordDialog.visible = true
  
  // 设置弹窗数据
  Object.assign(executeRecordDialogData, {
    ruleName: '金融标准映射规则',
    basicInfo: {
      ruleName: '金融标准映射规则',
      mappingDataset: '落标数据集（公示标准版/基础）：性别_姓名_同工同',
      startTime: row.startTime || '2025-05-05 10:47:44',
      belongAsset: 'dws_dddd_dddddd'
    },
    tableData: mockExecuteRecordData
  })
}
```

## 配置参数说明

### config 配置对象

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| type | String | 'RuleDetailContent' | 内容组件类型，可选值：'RuleDetailContent', 'ExecuteRecordContent' |
| title | String | '详情' | 弹窗标题 |
| width | String | '1400px' | 弹窗宽度 |
| closeText | String | '关闭' | 关闭按钮文本 |

### data 数据对象

#### RuleDetailContent 数据结构

```javascript
{
  ruleName: String,           // 规则名称
  basicInfo: {
    ruleName: String,         // 规则名称
    dataStandard: String,     // 数据标准
    standardSet: String,      // 标准集
    mappingObject: String     // 映射对象
  },
  tableData: Array           // 表格数据
}
```

#### ExecuteRecordContent 数据结构

```javascript
{
  ruleName: String,           // 规则名称
  basicInfo: {
    ruleName: String,         // 规则名称
    mappingDataset: String,   // 映射数据集
    startTime: String,        // 开始执行时间
    belongAsset: String       // 所属资产
  },
  tableData: Array           // 表格数据
}
```

## 扩展新的内容类型

如果需要添加新的内容类型，可以按照以下步骤：

1. 创建新的内容组件（如 `NewContent.vue`）
2. 在 `DetailDialog.vue` 的 `componentMap` 中注册新组件
3. 定义新组件的数据结构和配置

```javascript
// 在 DetailDialog.vue 中添加
const componentMap = {
  RuleDetailContent: defineAsyncComponent(() => import('./RuleDetailContent.vue')),
  ExecuteRecordContent: defineAsyncComponent(() => import('./ExecuteRecordContent.vue')),
  NewContent: defineAsyncComponent(() => import('./NewContent.vue'))  // 新增
}
```

## 注意事项

1. 确保传递的数据结构与对应的内容组件要求一致
2. 表格数据中的 `id` 字段是必需的，用于行展开功能
3. 组件使用异步加载，首次打开可能有轻微延迟
4. 弹窗关闭时会自动重置展开状态
