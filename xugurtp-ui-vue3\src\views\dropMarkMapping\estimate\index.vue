<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
       

        <!-- 标签页 -->
        <div class="tab-switch-container">
          <TabSwitch :title-list="tabList" @change="handleTabChange" />
        </div>

        <!-- 搜索区域 -->
        <el-form
          v-show="showSearch"
          ref="searchFormRef"
          class="search-box"
          :model="searchForm"
          :inline="true"
          label-width="70px"
        >
          <el-form-item label="关键词" prop="keyword">
            <el-input
              v-model="searchForm.keyword"
              placeholder="请输入标准中文名称/编码"
              style="width: 240px"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
            <el-button icon="Refresh" @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 操作工具栏 -->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <!-- 可以在这里添加操作按钮 -->
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            :columns="columns"
            @query-table="getList"
          />
        </el-row>

        <!-- 说明文字 -->
        <div class="info-text">
          <el-text type="info" size="small">
            所有属性监控规则均被覆盖的对象数占已配置监控规则的总对象数的百分比
          </el-text>
        </div>

        <!-- 表格区域 -->
        <div class="table-box">
          <el-table
            ref="tableRef"
            :data="tableData"
            height="100%"
            empty-text="暂无数据"
          >
        <el-table-column type="index" label="序号" width="60">
          <template #default="scope">
            {{ queryParams.pageSize * (queryParams.pageNum - 1) + (scope.$index + 1) }}
          </template>
        </el-table-column>

        <el-table-column v-if="columns[0].visible" prop="standardChineseName" label="标准中文名称" min-width="120" />

        <el-table-column v-if="columns[1].visible" prop="standardCode" label="标准编码" min-width="120" />

        <el-table-column v-if="columns[2].visible" prop="standardSet" label="所属标准集/" min-width="200" show-overflow-tooltip />

        <el-table-column v-if="columns[3].visible" prop="validMappingCount" label="有效映射关系数" min-width="140" align="center" />

        <el-table-column v-if="columns[4].visible" prop="monitorPassRate" label="监控通过率" min-width="120" align="center">
          <template #default="scope">
            <span :class="getPassRateClass(scope.row.monitorPassRate)">
              {{ scope.row.monitorPassRate }}
            </span>
          </template>
        </el-table-column>

        <el-table-column v-if="columns[5].visible" prop="lastEstimateTime" label="最近评估时间" min-width="160" />

        <el-table-column label="操作" fixed="right" min-width="120">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleViewDetail(scope.row)">
              评估详情
            </el-button>
          </template>
        </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <pagination
          v-show="total > 0"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          :total="total"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 评估详情抽屉 -->
    <el-drawer
      v-model="detailDrawer.visible"
      :title="detailDrawer.title"
      direction="rtl"
      size="80%"
      class="detail-drawer"
    >
      <div class="drawer-content">
        <div class="drawer-desc">
          <el-text type="info" size="small">
            展示选定不通过规则数(全部规则数，全部规则包括当前映射关系中已添加并开启监控的元数据监控和质量监控规则)
          </el-text>
        </div>

        <el-table
          :data="detailDrawer.tableData"
          height="calc(100vh - 200px)"
          :header-cell-class-name="addHeaderCellClassName"
          empty-text="暂无数据"
        >
          <el-table-column type="index" label="序号" width="60" />

          <el-table-column prop="mappingMetadataName" label="映射元数据名称" min-width="150" />

          <el-table-column prop="mappingMetadataCode" label="映射元数据编码" min-width="150" />

          <el-table-column prop="belongAsset" label="所属资产" min-width="180" />

          <el-table-column prop="estimateResult" label="评估结果" min-width="100" align="center">
            <template #default="scope">
              <el-tag
                :type="scope.row.estimateResult === '通过' ? 'success' : 'danger'"
                size="small"
              >
                {{ scope.row.estimateResult }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="failRuleRatio" label="不通过规则比" min-width="120" align="center" />

          <el-table-column prop="lastEstimateTime" label="最近评估时间" min-width="160" />

          <el-table-column label="操作" fixed="right" min-width="120">
            <template #default="scope">
              <el-button
                v-if="scope.row.estimateResult"
                type="text"
                size="small"
                @click="handleViewRuleDetail(scope.row)"
              >
                规则明细
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-drawer>

    <!-- 规则明细弹窗 -->
    <el-dialog
      v-model="ruleDetailDialog.visible"
      title="规则明细"
      width="1000px"
      :draggable="true"
      class="rule-detail-dialog"
    >
      <div class="dialog-content">
        <!-- 基础信息 -->
        <div class="basic-info">
          <div class="info-row">
            <div class="info-item">
              <span class="label">数据标准：</span>
              <span class="value">{{ ruleDetailDialog.basicInfo.dataStandard }}</span>
            </div>
            <div class="info-item">
              <span class="label">标准集：</span>
              <span class="value">{{ ruleDetailDialog.basicInfo.standardSet }}</span>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <span class="label">映射元数据：</span>
              <span class="value">{{ ruleDetailDialog.basicInfo.mappingMetadata }}</span>
            </div>
            <div class="info-item">
              <span class="label">所属资产：</span>
              <span class="value">{{ ruleDetailDialog.basicInfo.belongAsset }}</span>
            </div>
          </div>
        </div>

        <!-- 规则明细表格 -->
        <el-table
          :data="ruleDetailDialog.tableData"
          height="400px"
          :header-cell-class-name="addHeaderCellClassName"
          empty-text="暂无数据"
        >
          <el-table-column type="index" label="序号" width="60" />

          <el-table-column prop="relatedStandardAttr" label="关联标准属性" min-width="120" />

          <el-table-column prop="estimateResult" label="评估结果" min-width="100" align="center">
            <template #default="scope">
              <el-tag
                :type="scope.row.estimateResult === '通过' ? 'success' : 'danger'"
                size="small"
              >
                {{ scope.row.estimateResult }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="estimateDetail" label="评估详情" min-width="200">
            <template #default="scope">
              <div v-html="scope.row.estimateDetail"></div>
            </template>
          </el-table-column>

          <el-table-column prop="ruleType" label="规则类型" min-width="100" align="center" />

          <el-table-column label="操作" fixed="right" min-width="150">
            <template #default="scope">
              <div class="rule-detail-content">
                <div class="rule-detail-title">规则详情</div>
                <div class="rule-detail-item">监控类型: {{ scope.row.monitorType }}</div>
                <div class="rule-detail-item">标准属性: {{ scope.row.standardAttr }}</div>
                <div class="rule-detail-item">校验规则: {{ scope.row.validationRule }}</div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="ruleDetailDialog.visible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
/**
 * 数据标准评估页面
 * 功能：展示数据标准的落标评估结果，包括监控通过率、评估明细等
 * 作者：开发团队
 * 创建时间：2025-06-20
 */

import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import TabSwitch from '@/components/tabSwitch/index.vue'

// ==================== 响应式数据定义 ====================

/**
 * 当前激活的标签页
 * 可选值: 'standard' | 'asset'
 */
const activeTab = ref('standard')

/**
 * TabSwitch组件的标签列表
 */
const tabList = ref([
  {
    label: '标准视角',
    value: 'standard',
    isActive: true
  },
  {
    label: '资产视角（暂无）',
    value: 'asset',
    isActive: false,
    disabled: true
  }
])

/**
 * 表格组件引用
 */
const tableRef = ref()

/**
 * 搜索表单组件引用
 */
const searchFormRef = ref()

/**
 * 是否显示搜索区域
 */
const showSearch = ref(true)

/**
 * 搜索表单数据
 */
const searchForm = reactive({
  keyword: '' // 关键词搜索条件
})

/**
 * 分页查询参数
 */
const queryParams = reactive({
  pageNum: 1,   // 当前页码
  pageSize: 20  // 每页显示数量
})

/**
 * 表格数据列表
 */
const tableData = ref([])

/**
 * 数据总数
 */
const total = ref(0)

/**
 * 列显隐信息
 * 用于控制表格列的显示和隐藏
 */
const columns = ref([
  { key: 0, label: '标准中文名称', visible: true },
  { key: 1, label: '标准编码', visible: true },
  { key: 2, label: '所属标准集/', visible: true },
  { key: 3, label: '有效映射关系数', visible: true },
  { key: 4, label: '监控通过率', visible: true },
  { key: 5, label: '最近评估时间', visible: true }
])

// ==================== 弹窗和抽屉状态管理 ====================

/**
 * 评估详情抽屉状态
 * 用于显示标准的评估明细信息
 */
const detailDrawer = reactive({
  visible: false,   // 是否显示抽屉
  title: '',        // 抽屉标题
  tableData: []     // 抽屉中的表格数据
})

/**
 * 规则明细弹窗状态
 * 用于显示具体的评估规则详情
 */
const ruleDetailDialog = reactive({
  visible: false,   // 是否显示弹窗
  basicInfo: {      // 基础信息
    dataStandard: '',     // 数据标准
    standardSet: '',      // 标准集
    mappingMetadata: '',  // 映射元数据
    belongAsset: ''       // 所属资产
  },
  tableData: []     // 规则明细表格数据
})

// ==================== 模拟数据 ====================

/**
 * 评估结果模拟数据
 * 在实际项目中，这些数据应该从后端API获取
 */
const mockEstimateData = [
  {
    id: 1,
    standardChineseName: '标题',
    standardCode: 'title',
    standardSet: '基础标准集（公共标准/基础）：性别,姓名,员工ID',
    validMappingCount: 4,
    monitorPassRate: '12.5%',
    lastEstimateTime: '2025-06-06 10:33:33'
  },
  {
    id: 2,
    standardChineseName: '标题',
    standardCode: 'title',
    standardSet: '基础标准集（公共标准/基础）：性别,姓名,员工ID',
    validMappingCount: 8,
    monitorPassRate: '80%',
    lastEstimateTime: '2025-06-06 10:33:33'
  },
  {
    id: 3,
    standardChineseName: '标题',
    standardCode: 'title',
    standardSet: '基础标准集（公共标准/基础）：性别,姓名,员工ID',
    validMappingCount: 16,
    monitorPassRate: '95%',
    lastEstimateTime: '2025-06-06 10:33:33'
  }
]

/**
 * 评估详情抽屉模拟数据
 * 显示具体映射元数据的评估结果
 */
const mockDetailData = [
  {
    id: 1,
    mappingMetadataName: '标题',
    mappingMetadataCode: 'title',
    belongAsset: 'dws_ddd_ddddd',
    estimateResult: '不通过',
    failRuleRatio: '2/2',
    lastEstimateTime: '2025-06-06 10:33:33'
  },
  {
    id: 2,
    mappingMetadataName: '',
    mappingMetadataCode: '',
    belongAsset: '1111',
    estimateResult: '通过',
    failRuleRatio: '0/2',
    lastEstimateTime: ''
  },
  {
    id: 3,
    mappingMetadataName: '',
    mappingMetadataCode: '',
    belongAsset: 'dws_ddd_ddd2222dd',
    estimateResult: '',
    failRuleRatio: '',
    lastEstimateTime: ''
  },
  {
    id: 4,
    mappingMetadataName: '',
    mappingMetadataCode: '',
    belongAsset: 'dws_ddd_dd333333ddd',
    estimateResult: '',
    failRuleRatio: '',
    lastEstimateTime: ''
  }
]

/**
 * 规则明细弹窗模拟数据
 * 显示具体的评估规则和验证结果
 */
const mockRuleDetailData = [
  {
    id: 1,
    relatedStandardAttr: '数据类型',
    estimateResult: '不通过',
    estimateDetail: '标准值: STRING<br>对象值: varchar(64)',
    ruleType: '元数据',
    monitorType: '元数据监控',
    standardAttr: '数据类型',
    validationRule: '值相等则通过'
  },
  {
    id: 2,
    relatedStandardAttr: '是否可为空值',
    estimateResult: '不通过',
    estimateDetail: '标准值: 不为空<br>对象值: 有空值',
    ruleType: '内容质量',
    monitorType: '质量监控',
    standardAttr: '是否可为空值',
    validationRule: '不为空则通过'
  }
]

// ==================== 业务方法 ====================

/**
 * 处理TabSwitch标签页切换
 * @param {String} tabValue 切换到的标签页值
 */
const handleTabChange = (tabValue) => {
  if (tabValue === false) return // 点击当前激活的标签页时不处理

  // 检查是否是禁用的标签页
  const targetTab = tabList.value.find(tab => tab.value === tabValue)
  if (targetTab && targetTab.disabled) {
    ElMessage.warning('该功能暂未开放')
    return
  }

  activeTab.value = tabValue
  queryParams.pageNum = 1 // 重置页码
  getList() // 重新获取数据
}

/**
 * 根据通过率获取对应的CSS类名
 * @param {string} rate 通过率字符串，如 "80%"
 * @returns {string} CSS类名
 */
const getPassRateClass = (rate) => {
  const numRate = parseFloat(rate)
  if (numRate >= 80) return 'pass-rate-high'    // 高通过率：绿色
  if (numRate >= 50) return 'pass-rate-medium'  // 中等通过率：橙色
  return 'pass-rate-low'                        // 低通过率：红色
}

/**
 * 处理搜索操作
 * 重置页码并重新获取数据
 */
const handleSearch = () => {
  queryParams.pageNum = 1
  getList()
}

/**
 * 处理重置操作
 * 清空搜索条件并重新获取数据
 */
const handleReset = () => {
  searchForm.keyword = ''
  queryParams.pageNum = 1
  getList()
}

/**
 * 获取评估数据列表
 * 根据搜索条件过滤数据并更新表格
 */
const getList = () => {
  // 模拟API调用，实际项目中应该调用后端接口
  let data = [...mockEstimateData]

  // 根据关键词过滤数据（支持标准中文名称和标准编码搜索）
  if (searchForm.keyword) {
    data = data.filter(item =>
      item.standardChineseName.includes(searchForm.keyword) ||
      item.standardCode.includes(searchForm.keyword)
    )
  }

  total.value = data.length
  tableData.value = data
}

/**
 * 查看评估详情
 * 打开抽屉显示标准的详细评估结果
 * @param {Object} row 标准行数据
 */
const handleViewDetail = (row) => {
  detailDrawer.visible = true
  detailDrawer.title = `【${row.standardChineseName}】落标评估明细`
  detailDrawer.tableData = mockDetailData
}

/**
 * 查看规则明细
 * 打开弹窗显示具体的评估规则详情
 * @param {Object} row 评估记录行数据
 */
const handleViewRuleDetail = (row) => {
  ruleDetailDialog.visible = true
  ruleDetailDialog.basicInfo = {
    dataStandard: '标题',
    standardSet: '基础标准集',
    mappingMetadata: '基础标准集',
    belongAsset: row.belongAsset
  }
  ruleDetailDialog.tableData = mockRuleDetailData
}

// ==================== 监听器和生命周期 ====================

/**
 * 组件挂载后初始化数据
 */
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
@import '@/assets/styles/xg-ui/base.scss';

.app-container {
  width: 100%;
  height: 100%;

  & > .el-row {
    height: 100%;
    .el-col {
      height: 100%;
    }
  }

  .page-header {
    margin-bottom: 20px;

    h3 {
      margin: 0 0 8px 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .page-desc {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .tab-switch-container {
    margin-bottom: 20px;
    width: 300px; // 设置TabSwitch的宽度，适应较长的标签文本
  }

  .estimate-tabs {
    margin-bottom: 20px;

    :deep(.el-tabs__header) {
      margin-bottom: 0;
    }
  }

  .search-box {
    margin: 0;
    text-align: right;
    .el-form-item--default {
      margin-bottom: 20px;
      &:last-child {
        margin-right: 0;
      }
    }
  }

  .info-text {
    margin-bottom: 16px;
    padding: 12px;
    background: #f0f9ff;
    border: 1px solid #b3d8ff;
    border-radius: 6px;
  }

  .table-box {
    height: calc(100% - 350px);
  }
}

// 通过率样式
.pass-rate-high {
  color: #67c23a;
  font-weight: 600;
}

.pass-rate-medium {
  color: #e6a23c;
  font-weight: 600;
}

.pass-rate-low {
  color: #f56c6c;
  font-weight: 600;
}

// 抽屉样式
:deep(.detail-drawer) {
  .drawer-content {
    padding: 20px;

    .drawer-desc {
      margin-bottom: 20px;
      padding: 12px;
      background: #f0f9ff;
      border: 1px solid #b3d8ff;
      border-radius: 6px;
    }
  }
}

// 弹窗样式
:deep(.rule-detail-dialog) {
  .dialog-content {
    .basic-info {
      margin-bottom: 20px;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;

      .info-row {
        display: flex;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .info-item {
          flex: 1;
          display: flex;
          align-items: center;

          .label {
            font-weight: 600;
            color: #606266;
            margin-right: 8px;
            min-width: 80px;
          }

          .value {
            color: #303133;
          }
        }
      }
    }

    .rule-detail-content {
      .rule-detail-title {
        font-weight: 600;
        color: #303133;
        margin-bottom: 8px;
      }

      .rule-detail-item {
        color: #606266;
        font-size: 12px;
        line-height: 1.4;
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>