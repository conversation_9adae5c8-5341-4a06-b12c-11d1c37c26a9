<template>
  <div class="execute-record-content">
    <!-- 落标执行记录 -->
    <div class="section">
      <div class="section-header">
        <el-icon class="section-icon"><Document /></el-icon>
        <span class="section-title">落标执行记录</span>
      </div>
      
      <!-- 规则名称 -->
      <div class="rule-name-section">
        <div class="rule-name-header">
          <el-icon class="rule-icon"><Setting /></el-icon>
          <span class="rule-name">{{ data.ruleName || '金融标准映射规则' }}</span>
        </div>
        
        <!-- 基础信息卡片 -->
        <div class="info-cards">
          <div class="info-card">
            <div class="card-label">规则名称</div>
            <div class="card-value">{{ data.basicInfo?.ruleName || '金融标准映射规则' }}</div>
          </div>
          <div class="info-card">
            <div class="card-label">映射数据集/对象</div>
            <div class="card-value">{{ data.basicInfo?.mappingDataset || '基础标准集（公示标准版/基础）：性别_姓名_员工ID' }}</div>
          </div>
          <div class="info-card">
            <div class="card-label">开始执行时间</div>
            <div class="card-value">{{ data.basicInfo?.startTime || '2025-05-05 10:47:44' }}</div>
          </div>
          <div class="info-card">
            <div class="card-label">所属资产</div>
            <div class="card-value">{{ data.basicInfo?.belongAsset || 'dws_dddd_dddddd' }}</div>
          </div>
        </div>
        
        <!-- 落标明细表格 -->
        <div class="execute-record-table">
          <el-table
            :data="data.tableData || defaultTableData"
            :header-cell-class-name="addHeaderCellClassName"
            empty-text="暂无数据"
            @row-click="handleRowClick"
            row-key="id"
            :expand-row-keys="expandedRows"
            @expand-change="handleExpandChange"
          >
            <el-table-column type="expand" width="30">
              <template #default="{ row }">
                <div class="expand-content">
                  <div class="expand-section">
                    <div class="expand-title">有效映射关系表（4条）</div>
                    <div class="mapping-grid">
                      <div v-for="(mapping, index) in row.mappingRelations" :key="index" class="mapping-item">
                        <div class="mapping-row">
                          <span class="mapping-label">映射元数据编码：</span>
                          <span class="mapping-value">{{ mapping.metadataCode }}</span>
                        </div>
                        <div class="mapping-row">
                          <span class="mapping-label">映射元数据编码：</span>
                          <span class="mapping-value">{{ mapping.metadataCode }}</span>
                        </div>
                        <div class="mapping-row">
                          <span class="mapping-label">元数据路径：</span>
                          <span class="mapping-value">{{ mapping.metadataPath }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column prop="standardChineseName" label="标准中文名称" min-width="120" />
            <el-table-column prop="standardCode" label="标准编码" min-width="120" />
            <el-table-column prop="validMappingCount" label="有效映射关系数" min-width="140" align="center" />
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Document, Setting } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  },
  config: {
    type: Object,
    default: () => ({})
  },
  expandedRows: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['row-click', 'expand-change'])

// 默认表格数据
const defaultTableData = ref([
  {
    id: 1,
    standardChineseName: '员工ID',
    standardCode: 'USERID',
    validMappingCount: 4,
    mappingRelations: [
      {
        metadataCode: 'user_id',
        metadataPath: '111/222/表名'
      },
      {
        metadataCode: 'user_id',
        metadataPath: '111/222/表名'
      },
      {
        metadataCode: 'user_id',
        metadataPath: '111/222/表名'
      },
      {
        metadataCode: 'user_id',
        metadataPath: '111/222/表名'
      }
    ]
  },
  {
    id: 2,
    standardChineseName: '性别',
    standardCode: 'GENDER',
    validMappingCount: 3,
    mappingRelations: [
      {
        metadataCode: 'gender',
        metadataPath: '111/222/表名'
      },
      {
        metadataCode: 'gender',
        metadataPath: '111/222/表名'
      },
      {
        metadataCode: 'gender',
        metadataPath: '111/222/表名'
      }
    ]
  },
  {
    id: 3,
    standardChineseName: '姓名',
    standardCode: 'NAME',
    validMappingCount: 23,
    mappingRelations: []
  }
])

// 方法
const addHeaderCellClassName = () => 'table-header-cell'

const handleRowClick = (row) => {
  emit('row-click', row)
}

const handleExpandChange = (row, expandedRowsData) => {
  emit('expand-change', row, expandedRowsData)
}
</script>

<style lang="scss" scoped>
.execute-record-content {
  .section {
    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 12px;
      border-bottom: 2px solid #e4e7ed;
      
      .section-icon {
        margin-right: 8px;
        color: #409eff;
        font-size: 18px;
      }
      
      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }
    
    .rule-name-section {
      .rule-name-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        
        .rule-icon {
          margin-right: 8px;
          color: #67c23a;
          font-size: 16px;
        }
        
        .rule-name {
          font-size: 14px;
          font-weight: 600;
          color: #303133;
        }
      }
      
      .info-cards {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 16px;
        margin-bottom: 20px;
        
        .info-card {
          padding: 12px;
          background: #f8f9fa;
          border-radius: 6px;
          border: 1px solid #e4e7ed;
          
          .card-label {
            font-size: 12px;
            color: #909399;
            margin-bottom: 4px;
          }
          
          .card-value {
            font-size: 14px;
            color: #303133;
            font-weight: 500;
          }
        }
      }
    }
  }
}

// 展开内容样式
:deep(.el-table__expanded-cell) {
  padding: 20px !important;
  background-color: #fafbfc;
  
  .expand-content {
    .expand-section {
      .expand-title {
        font-size: 14px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 1px solid #e4e7ed;
      }
      
      .mapping-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 16px;
        
        .mapping-item {
          padding: 12px;
          background: #fff;
          border: 1px solid #e4e7ed;
          border-radius: 6px;
          
          .mapping-row {
            display: flex;
            margin-bottom: 8px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            .mapping-label {
              font-size: 12px;
              color: #909399;
              margin-right: 8px;
              min-width: 80px;
            }
            
            .mapping-value {
              font-size: 12px;
              color: #303133;
              flex: 1;
            }
          }
        }
      }
    }
  }
}

:deep(.el-table) {
  .table-header-cell {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 600;
  }
  
  .el-table__row {
    cursor: pointer;
    
    &:hover {
      background-color: #f5f7fa;
    }
  }
}
</style>
