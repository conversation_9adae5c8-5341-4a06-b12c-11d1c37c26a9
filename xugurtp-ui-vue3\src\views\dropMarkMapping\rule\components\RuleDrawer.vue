<template>
    <el-drawer v-model="visible" :title="title" direction="rtl" size="60%" class="rule-drawer">
        <div class="drawer-content">
            <el-form ref="ruleFormRef" :model="form" :rules="rules" label-width="140px" label-position="left">
                <!-- 基本信息 -->
                <div class="form-section">
                    <h3 class="section-title">基本信息</h3>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="映射规则名称" prop="ruleName" required>
                                <el-input v-model="form.ruleName" placeholder="请输入规则名称" style="width: 400px" />
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label="描述" prop="description">
                                <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入规则描述"
                                    style="width: 400px" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>

                <!-- 圈选数据标准 -->
                <div class="form-section">
                    <!-- <h3 class="section-title">圈选数据标准</h3>
                    <p class="section-desc">圈选指定标准集/标准，圈选的资产对象基于配置进行映射匹配</p> -->
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="来源标准集/标准" prop="sourceStandard" required>
                                <el-row :gutter="20">
                                    <el-col :span="12">
                                        <el-select v-model="form.sourceStandard" placeholder="请选择标准目录"
                                            style="width: 90%" filterable>
                                            <el-option v-for="item in standardOptions" :key="item.value"
                                                :label="item.label" :value="item.value" />
                                        </el-select>
                                    </el-col>
                                    <el-col :span="12">

                                        <el-tree :data="standardTreeData" show-checkbox node-key="id"
                                            :default-expanded-keys="[1]" :default-checked-keys="[]"
                                            :props="{ children: 'children', label: 'label' }" style="width: 50%" />
                                    </el-col>
                                </el-row>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="映射数据对象" prop="mappingDataObject" required>
                                <el-select v-model="form.mappingDataObject" placeholder="可以多选择数据库/数据表/元数据，可多选"
                                    style="width: 400px" multiple filterable>
                                    <el-option v-for="item in dataObjectOptions" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>

                <!-- 圈选资产对象 -->
                <div class="form-section">
                    <h3 class="section-title">映射配置</h3>
                    <el-text type="info" size="small">无法删除，至少配置1条规则</el-text>


                </div>

                <!-- 映射配置 -->
                <div class="form-section">


                    <!-- 相似度匹配配置 -->
                    <el-form-item v-if="form.mappingMethod === 'similarity'" label="相似度阈值">
                        <el-input-number v-model="form.similarityThreshold" :min="0" :max="100" style="width: 120px" />
                        <span style="margin-left: 8px">%</span>
                    </el-form-item>

                    <!-- 映射规则配置 -->
                    <div class="mapping-rules">
                        <div class="rule-header">
                        </div>

                        <div class="rules-container">
                            <div v-for="(rule, index) in form.mappingRules" :key="index" class="mapping-rule-item">
                                <div class="rule-row">
                                    <el-select v-model="rule.leftField" placeholder="字段名称（单选）" style="width: 150px">
                                        <el-option label="字段名称" value="fieldName" />
                                        <el-option label="字段名称（中文）" value="fieldNameCn" />
                                        <el-option label="字段名称（英文）" value="fieldNameEn" />
                                        <el-option label="字段编码" value="fieldCode" />
                                    </el-select>

                                    <el-select v-model="rule.leftType" style="width: 100px">
                                        <el-option label="STRING" value="STRING" />
                                    </el-select>

                                    <span class="operator">=</span>

                                    <el-select v-model="rule.rightField" placeholder="标准中文名称（单选）" style="width: 150px">
                                        <el-option label="标准中文名称" value="standardNameCn" />
                                    </el-select>

                                    <el-select v-model="rule.rightType" style="width: 100px">
                                        <el-option label="STRING" value="STRING" />
                                    </el-select>

                                    <el-button v-if="form.mappingRules.length > 1" type="danger" icon="Delete" circle
                                        size="small" @click="removeRule(index)" />
                                </div>

                                <!-- 连接线和连接符 -->
                                <div v-if="index < form.mappingRules.length - 1" class="rule-connector">
                                    <div class="connector-line">
                                        <div class="line-top"></div>
                                        <div class="line-horizontal"></div>
                                        <div class="line-bottom"></div>
                                    </div>
                                    <div class="connector-select">
                                        <el-select v-model="rule.connector" style="width: 80px" size="small">
                                            <el-option label="且" value="AND" />
                                            <el-option label="或" value="OR" />
                                        </el-select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="add-rule-btn">
                            <el-button type="primary" icon="Plus" @click="addRule">
                                添加规则
                            </el-button>
                        </div>
                    </div>
                </div>

                <!-- 执行配置 -->
                <div class="form-section">
                    <h3 class="section-title">执行配置</h3>

                    <el-form-item label="执行方式" prop="executeMethod" required>
                        <el-radio-group v-model="form.executeMethod">
                            <el-radio label="scheduled">定时执行</el-radio>
                            <el-radio label="manual">手动执行</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item v-if="form.executeMethod === 'scheduled'" label="执行周期" prop="executeCycle">
                        <div class="schedule-config">
                            <el-text type="info">沿用以前的调度周期设置的样式</el-text>
                            <div class="schedule-placeholder">
                                BUYNO
                            </div>
                        </div>
                    </el-form-item>
                </div>
            </el-form>
        </div>

        <template #footer>
            <div class="drawer-footer">
                <el-button @click="handleCancel">取消</el-button>
                <el-button type="primary" @click="handleSave">确定</el-button>
            </div>
        </template>
    </el-drawer>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: '新建映射规则'
    },
    data: {
        type: Object,
        default: () => ({})
    }
})

// Emits
const emit = defineEmits(['update:modelValue', 'save'])

// 响应式数据
const visible = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
})

const ruleFormRef = ref()

const form = reactive({
    ruleName: '',
    description: '',
    sourceStandard: '',
    mappingDataObject: [],
    mappingMethod: 'attribute',
    similarityThreshold: 80,
    mappingRules: [
        {
            leftField: 'fieldName',
            leftType: 'STRING',
            rightField: 'standardNameCn',
            rightType: 'STRING',
            connector: 'AND'
        },
        {
            leftField: 'fieldNameCn',
            leftType: 'STRING',
            rightField: 'standardNameCn',
            rightType: 'STRING',
            connector: 'OR'
        },
        {
            leftField: 'fieldCode',
            leftType: 'STRING',
            rightField: 'standardNameCn',
            rightType: 'STRING',
            connector: 'AND'
        }
    ],
    executeMethod: 'manual',
    executeCycle: ''
})

const rules = {
    ruleName: [
        { required: true, message: '请输入映射规则名称', trigger: 'blur' }
    ],
    sourceStandard: [
        { required: true, message: '请选择来源标准集/标准', trigger: 'change' }
    ],
    mappingDataObject: [
        { required: true, message: '请选择映射数据对象', trigger: 'change' }
    ],
    mappingMethod: [
        { required: true, message: '请选择映射方式', trigger: 'change' }
    ],
    executeMethod: [
        { required: true, message: '请选择执行方式', trigger: 'change' }
    ]
}

const standardOptions = [
    { label: '标准集测试目录（1）', value: 'test_dir_1' },
    { label: '测试标准集', value: 'test_standard_set' }
]

const standardTreeData = [
    {
        id: 1,
        label: '标准集测试目录（1）',
        children: [
            {
                id: 2,
                label: '测试标准集'
            }
        ]
    }
]

const dataObjectOptions = [
    { label: '数据库1/表1/字段1', value: 'db1_table1_field1' },
    { label: '数据库1/表1/字段2', value: 'db1_table1_field2' },
    { label: '数据库2/表2/字段1', value: 'db2_table2_field1' }
]

// 方法
const resetForm = () => {
    Object.assign(form, {
        ruleName: '',
        description: '',
        sourceStandard: '',
        mappingDataObject: [],
        mappingMethod: 'attribute',
        similarityThreshold: 80,
        mappingRules: [
            {
                leftField: '',
                leftType: 'STRING',
                rightField: '',
                rightType: 'STRING',
                connector: 'AND'
            }
        ],
        executeMethod: 'manual',
        executeCycle: ''
    })
}

const addRule = () => {
    form.mappingRules.push({
        leftField: '',
        leftType: 'STRING',
        rightField: '',
        rightType: 'STRING',
        connector: 'AND'
    })
}

const removeRule = (index) => {
    if (form.mappingRules.length > 1) {
        form.mappingRules.splice(index, 1)
    }
}

const handleCancel = () => {
    visible.value = false
}

const handleSave = () => {
    ruleFormRef.value.validate((valid) => {
        if (valid) {
            emit('save', { ...form })
            visible.value = false
        } else {
            ElMessage.error('请完善表单信息')
        }
    })
}

// 监听数据变化
watch(() => props.data, (newData) => {
    if (newData && Object.keys(newData).length > 0) {
        Object.assign(form, {
            ruleName: newData.ruleName || '',
            description: newData.description || '',
            // 其他字段根据实际需要填充
        })
    }
}, { immediate: true })

// 监听visible变化，关闭时重置表单
watch(visible, (newVal) => {
    if (!newVal) {
        nextTick(() => {
            resetForm()
            ruleFormRef.value?.clearValidate()
        })
    }
})
</script>

<style lang="scss" scoped>
.rule-drawer {
    :deep(.el-drawer__body) {
        padding: 0;
    }

    .drawer-content {
        padding: 20px;
        height: calc(100% - 60px);
        overflow-y: auto;

        .form-section {
            margin-bottom: 32px;

            .section-title {
                margin: 0 0 16px 0;
                font-size: 16px;
                font-weight: 600;
                color: #303133;
                border-bottom: 1px solid #e4e7ed;
                padding-bottom: 8px;
            }

            .section-desc {
                margin: 0 0 16px 0;
                color: #606266;
                font-size: 14px;
            }

            .search-hint {
                margin-top: 8px;
                margin-bottom: 16px;
            }

            .standard-tree {
                border: 1px solid #dcdfe6;
                border-radius: 4px;
                padding: 12px;
                max-height: 200px;
                overflow-y: auto;
            }

            .mapping-rules {
                .rule-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 16px;

                    span:first-child {
                        font-weight: 600;
                        color: #303133;
                    }
                }

                .rules-container {
                    max-height: 300px;
                    overflow-y: auto;
                    border: 1px solid #e4e7ed;
                    border-radius: 6px;
                    padding: 16px;
                    margin-bottom: 16px;
                    background: #fafafa;

                    &::-webkit-scrollbar {
                        width: 6px;
                    }

                    &::-webkit-scrollbar-track {
                        background: #f1f1f1;
                        border-radius: 3px;
                    }

                    &::-webkit-scrollbar-thumb {
                        background: #c1c1c1;
                        border-radius: 3px;

                        &:hover {
                            background: #a8a8a8;
                        }
                    }
                }

                .mapping-rule-item {
                    position: relative;
                    margin-bottom: 0;

                    .rule-row {
                        display: flex;
                        align-items: center;
                        gap: 12px;
                        padding: 12px;
                        background: #fff;
                        border: 1px solid #e4e7ed;
                        border-radius: 6px;
                        margin-bottom: 8px;

                        .operator {
                            font-weight: 600;
                            color: #303133;
                            padding: 0 8px;
                            font-size: 16px;
                        }
                    }

                    .rule-connector {
                        position: relative;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 40px;
                        margin-bottom: 8px;

                        .connector-line {
                            position: absolute;
                            left: 50%;
                            transform: translateX(-50%);
                            width: 2px;
                            height: 100%;
                            display: flex;
                            flex-direction: column;

                            .line-top {
                                flex: 1;
                                border-left: 2px dashed #d3d3d3;
                            }

                            .line-horizontal {
                                width: 60px;
                                height: 2px;
                                border-top: 2px dashed #d3d3d3;
                                margin-left: -29px;
                                position: relative;
                                z-index: 1;
                            }

                            .line-bottom {
                                flex: 1;
                                border-left: 2px dashed #d3d3d3;
                            }
                        }

                        .connector-select {
                            position: relative;
                            z-index: 2;
                            background: #fafafa;
                            padding: 0 8px;
                        }
                    }
                }

                .add-rule-btn {
                    text-align: center;
                    padding-top: 8px;
                }
            }

            .schedule-config {
                .schedule-placeholder {
                    margin-top: 12px;
                    padding: 20px;
                    background: #f5f7fa;
                    border: 1px dashed #d3d3d3;
                    border-radius: 4px;
                    text-align: center;
                    color: #909399;
                    font-size: 14px;
                }
            }
        }
    }

    .drawer-footer {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        padding: 16px 20px;
        border-top: 1px solid #e4e7ed;
        background: #fff;
    }
}
</style>
