<template>
  <div class="rule-detail-content">
    <!-- 规则明细 -->
    <div class="section">
      <div class="section-header">
        <el-icon class="section-icon"><Document /></el-icon>
        <span class="section-title">规则明细</span>
      </div>
      
      <!-- 规则名称 -->
      <div class="rule-name-section">
        <div class="rule-name-header">
          <el-icon class="rule-icon"><Setting /></el-icon>
          <span class="rule-name">{{ data.ruleName || '我是规则名称' }}</span>
        </div>
        
        <!-- 基础信息卡片 -->
        <div class="info-cards">
          <div class="info-card">
            <div class="card-label">规则名称</div>
            <div class="card-value">{{ data.basicInfo?.ruleName || '规则名称' }}</div>
          </div>
          <div class="info-card">
            <div class="card-label">数据标准</div>
            <div class="card-value">{{ data.basicInfo?.dataStandard || '数据标准' }}</div>
          </div>
          <div class="info-card">
            <div class="card-label">标准集</div>
            <div class="card-value link-text">{{ data.basicInfo?.standardSet || '基础标准集' }}</div>
          </div>
          <div class="info-card">
            <div class="card-label">映射对象</div>
            <div class="card-value">{{ data.basicInfo?.mappingObject || 'dws_dddd_dddddd' }}</div>
          </div>
        </div>
        
        <!-- 规则明细表格 -->
        <div class="rule-detail-table">
          <el-table
            :data="data.tableData || defaultTableData"
            :header-cell-class-name="addHeaderCellClassName"
            empty-text="暂无数据"
            @row-click="handleRowClick"
            row-key="id"
            :expand-row-keys="expandedRows"
            @expand-change="handleExpandChange"
          >
            <el-table-column type="expand" width="30">
              <template #default="{ row }">
                <div class="expand-content">
                  <div class="expand-section">
                    <div class="expand-title">规则详情</div>
                    <div class="expand-grid">
                      <div class="expand-item">
                        <span class="expand-label">标准值:</span>
                        <span class="expand-value">{{ row.standardValue }}</span>
                      </div>
                      <div class="expand-item">
                        <span class="expand-label">对象值:</span>
                        <span class="expand-value">{{ row.objectValue }}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="expand-section">
                    <div class="expand-title">评估详情</div>
                    <div class="expand-grid">
                      <div class="expand-item">
                        <span class="expand-label">标准值:</span>
                        <span class="expand-value">{{ row.evaluationStandardValue || '不为空' }}</span>
                      </div>
                      <div class="expand-item">
                        <span class="expand-label">对象值:</span>
                        <span class="expand-value">{{ row.evaluationObjectValue || '有空值' }}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="expand-section">
                    <div class="expand-title">规则类型</div>
                    <div class="expand-grid">
                      <div class="expand-item full-width">
                        <span class="expand-label">{{ row.ruleType }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column prop="standardAttribute" label="关联标准属性" min-width="120" />
            
            <el-table-column prop="evaluationResult" label="评估结果" min-width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="row.evaluationResult === '通过' ? 'success' : 'danger'" size="small">
                  {{ row.evaluationResult }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column prop="evaluationDetail" label="评估详情" min-width="200">
              <template #default="{ row }">
                <div class="evaluation-detail">
                  <div>标准值: {{ row.standardValue }}</div>
                  <div>对象值: {{ row.objectValue }}</div>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column prop="ruleType" label="规则类型" min-width="100" align="center" />
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Document, Setting } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  },
  config: {
    type: Object,
    default: () => ({})
  },
  expandedRows: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['row-click', 'expand-change'])

// 默认表格数据
const defaultTableData = ref([
  {
    id: 1,
    standardAttribute: '员工ID',
    evaluationResult: '不通过',
    standardValue: 'STRING',
    objectValue: 'varchar(64)',
    ruleType: '元数据'
  },
  {
    id: 2,
    standardAttribute: '是否可为空值',
    evaluationResult: '不通过',
    standardValue: '不为空',
    objectValue: '有空值',
    ruleType: '内容质量'
  }
])

// 方法
const addHeaderCellClassName = () => 'table-header-cell'

const handleRowClick = (row) => {
  emit('row-click', row)
}

const handleExpandChange = (row, expandedRowsData) => {
  emit('expand-change', row, expandedRowsData)
}
</script>

<style lang="scss" scoped>
.rule-detail-content {
  .section {
    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 12px;
      border-bottom: 2px solid #e4e7ed;
      
      .section-icon {
        margin-right: 8px;
        color: #409eff;
        font-size: 18px;
      }
      
      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }
    
    .rule-name-section {
      .rule-name-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        
        .rule-icon {
          margin-right: 8px;
          color: #67c23a;
          font-size: 16px;
        }
        
        .rule-name {
          font-size: 14px;
          font-weight: 600;
          color: #303133;
        }
      }
      
      .info-cards {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 16px;
        margin-bottom: 20px;
        
        .info-card {
          padding: 12px;
          background: #f8f9fa;
          border-radius: 6px;
          border: 1px solid #e4e7ed;
          
          .card-label {
            font-size: 12px;
            color: #909399;
            margin-bottom: 4px;
          }
          
          .card-value {
            font-size: 14px;
            color: #303133;
            font-weight: 500;
            
            &.link-text {
              color: #409eff;
              cursor: pointer;
              
              &:hover {
                text-decoration: underline;
              }
            }
          }
        }
      }
      
      .rule-detail-table {
        .evaluation-detail {
          font-size: 12px;
          line-height: 1.4;
          
          div {
            margin-bottom: 2px;
            
            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
}

// 展开内容样式
:deep(.el-table__expanded-cell) {
  padding: 20px !important;
  background-color: #fafbfc;
  
  .expand-content {
    .expand-section {
      margin-bottom: 20px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .expand-title {
        font-size: 14px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #e4e7ed;
      }
      
      .expand-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        
        .expand-item {
          display: flex;
          align-items: center;
          
          &.full-width {
            grid-column: 1 / -1;
          }
          
          .expand-label {
            font-size: 12px;
            color: #909399;
            margin-right: 8px;
            min-width: 60px;
          }
          
          .expand-value {
            font-size: 12px;
            color: #303133;
          }
        }
      }
    }
  }
}

:deep(.el-table) {
  .table-header-cell {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 600;
  }
  
  .el-table__row {
    cursor: pointer;
    
    &:hover {
      background-color: #f5f7fa;
    }
  }
}
</style>
