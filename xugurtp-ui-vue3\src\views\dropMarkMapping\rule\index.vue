<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">


        <!-- 搜索区域 -->
        <el-form
          v-show="showSearch"
          ref="searchFormRef"
          class="search-box"
          :model="searchForm"
          :inline="true"
          label-width="80px"
        >
          <el-form-item label="规则名称" prop="ruleName">
            <el-input
              v-model="searchForm.ruleName"
              placeholder="请输入规则名称"
              clearable
              style="width: 240px"
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item label="标准目录" prop="standardCatalog">
            <el-select
              v-model="searchForm.standardCatalog"
              placeholder="请选择标准目录"
              clearable
              style="width: 240px"
            >
              <el-option label="金融标准" value="finance" />
              <el-option label="数据标准" value="data" />
              <el-option label="技术标准" value="tech" />
              <el-option label="业务标准" value="business" />
            </el-select>
          </el-form-item>
          <el-form-item label="标准名称" prop="standardName">
            <el-input
              v-model="searchForm.standardName"
              placeholder="请输入标准名称"
              clearable
              style="width: 240px"
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
            <el-button icon="Refresh" @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 操作按钮区域 -->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleCreate">
              新建映射规则
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="Delete"
              :disabled="selectedRows.length === 0"
              @click="handleBatchDelete"
            >
              批量删除
            </el-button>
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            :columns="columns"
            @query-table="getList"
          />
        </el-row>

        <!-- 表格区域 -->
        <div class="table-box">
          <el-table
            ref="tableRef"
            :data="tableData"
            height="100%"
            @selection-change="handleSelectionChange"
          >
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="序号" width="60">
          <template #default="scope">
            {{ queryParams.pageSize * (queryParams.pageNum - 1) + (scope.$index + 1) }}
          </template>
        </el-table-column>

        <el-table-column v-if="columns[0].visible" prop="ruleName" label="规则名称" min-width="180">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">
              {{ scope.row.ruleName }}
            </el-button>
          </template>
        </el-table-column>

        <el-table-column v-if="columns[1].visible" prop="mappingStandard" label="映射标准 / 标准集" min-width="180" />

        <el-table-column v-if="columns[2].visible" prop="standardTemplate" label="标准模板" min-width="120" />

        <el-table-column v-if="columns[3].visible" prop="mappingMethod" label="映射方式" min-width="150" />

        <el-table-column v-if="columns[4].visible" prop="executeMethod" label="执行方式" min-width="100" />

        <el-table-column v-if="columns[5].visible" prop="creator" label="创建人" min-width="100" />

        <el-table-column v-if="columns[6].visible" prop="updateTime" label="最近更新时间" min-width="160" />

        <el-table-column v-if="columns[7].visible" prop="lastExecuteRecord" label="最近执行记录" min-width="200">
          <template #default="scope">
            <span v-if="scope.row.lastExecuteRecord">
              {{ scope.row.lastExecuteRecord }}
              <el-tag
                :type="scope.row.executeStatus === '成功' ? 'success' : 'danger'"
                size="small"
                style="margin-left: 8px"
              >
                {{ scope.row.executeStatus }}
              </el-tag>
            </span>
            <span v-else class="text-muted">未执行</span>
          </template>
        </el-table-column>

        <el-table-column v-if="columns[8].visible" prop="description" label="描述" min-width="150" show-overflow-tooltip />

        <el-table-column v-if="columns[9].visible" prop="status" label="生效状态" min-width="100">
          <template #default="scope">
            <el-tag
              :type="scope.row.status === '生效' ? 'success' : 'info'"
              size="small"
            >
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" fixed="right" min-width="280">
          <template #default="scope">
            <div class="action-buttons">
              <!-- 常用操作 -->
              <div class="primary-actions">
                <el-button type="text" size="small" @click="handleView(scope.row)">
                  查看
                </el-button>
                <el-button type="text" size="small" @click="handleEdit(scope.row)">
                  编辑
                </el-button>
                <el-button type="text" size="small" @click="handleExecute(scope.row)">
                  执行
                </el-button>
              </div>

              <!-- 更多操作 - 折叠 -->
              <el-dropdown trigger="click" @command="(command) => handleDropdownCommand(command, scope.row)">
                <el-button type="text" size="small">
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="copy">复制</el-dropdown-item>
                    <el-dropdown-item command="executeRecord">落标执行记录</el-dropdown-item>
                    <el-dropdown-item command="delete" divided style="color: #f56c6c">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <pagination
          v-show="total > 0"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          :total="total"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 落标执行记录抽屉 -->
    <ExecuteRecordDrawer
      v-model="executeRecordDrawer.visible"
      :rule-name="executeRecordDrawer.ruleName"
      @view-detail="handleViewDetail"
      @view-log="handleViewLog"
    />

    <!-- 落标明细弹窗 -->
    <DetailDialog
      v-model="detailDialog.visible"
      :data="detailDialog.data"
    />

    <!-- 新建/编辑映射规则抽屉 -->
    <RuleDrawer
      v-model="ruleDrawer.visible"
      :title="ruleDrawer.title"
      :data="ruleDrawer.data"
      @save="handleSaveRule"
    />
  </div>
</template>

<script setup>
/**
 * 映射规则管理页面
 * 功能：管理数据映射规则的创建、编辑、删除、执行等操作
 * 作者：开发团队
 * 创建时间：2025-06-20
 */

import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import { ExecuteRecordDrawer, DetailDialog, RuleDrawer } from './components'

// ==================== 响应式数据定义 ====================



/**
 * 表格组件引用
 */
const tableRef = ref()

/**
 * 搜索表单组件引用
 */
const searchFormRef = ref()

/**
 * 是否显示搜索区域
 */
const showSearch = ref(true)

/**
 * 搜索表单数据
 * @type {Object}
 */
const searchForm = reactive({
  ruleName: '',        // 规则名称搜索条件
  standardCatalog: '', // 标准目录搜索条件
  standardName: ''     // 标准名称搜索条件
})

/**
 * 分页查询参数
 * @type {Object}
 */
const queryParams = reactive({
  pageNum: 1,   // 当前页码
  pageSize: 20  // 每页显示数量
})

/**
 * 表格数据列表
 */
const tableData = ref([])

/**
 * 数据总数
 */
const total = ref(0)

/**
 * 选中的行数据
 */
const selectedRows = ref([])

/**
 * 列显隐信息
 * 用于控制表格列的显示和隐藏
 */
const columns = ref([
  { key: 0, label: '规则名称', visible: true },
  { key: 1, label: '映射标准 / 标准集', visible: true },
  { key: 2, label: '标准模板', visible: true },
  { key: 3, label: '映射方式', visible: true },
  { key: 4, label: '执行方式', visible: true },
  { key: 5, label: '创建人', visible: true },
  { key: 6, label: '最近更新时间', visible: true },
  { key: 7, label: '最近执行记录', visible: true },
  { key: 8, label: '描述', visible: true },
  { key: 9, label: '生效状态', visible: true }
])

// ==================== 弹窗和抽屉状态管理 ====================

/**
 * 落标执行记录抽屉状态
 * 用于显示规则的执行历史记录
 */
const executeRecordDrawer = reactive({
  visible: false,   // 是否显示抽屉
  ruleName: ''      // 当前查看的规则名称
})

/**
 * 落标明细弹窗状态
 * 用于显示执行记录的详细信息
 */
const detailDialog = reactive({
  visible: false,   // 是否显示弹窗
  data: {}          // 弹窗显示的数据
})

/**
 * 规则编辑抽屉状态
 * 用于新建或编辑映射规则
 */
const ruleDrawer = reactive({
  visible: false,           // 是否显示抽屉
  title: '新建映射规则',    // 抽屉标题
  data: {}                  // 编辑的规则数据
})

// ==================== 模拟数据 ====================

/**
 * 映射规则模拟数据
 * 在实际项目中，这些数据应该从后端API获取
 */
const mockData = [
  {
    id: 1,
    ruleName: '金融标准映射规则',
    mappingStandard: '标准集 / 标准 123',
    standardTemplate: '模板 AAA',
    mappingMethod: '按对象属性匹配',
    executeMethod: '手动执行',
    creator: '小赵',
    updateTime: '2025-06-06 10:22:22',
    lastExecuteRecord: '2025-06-06 10:33:33',
    executeStatus: '成功',
    description: '12312321321212',
    status: '生效',
    standardCatalog: 'finance',
    standardName: '金融数据标准'
  },
  {
    id: 2,
    ruleName: '金融 xxx 规则',
    mappingStandard: '标准集 555',
    standardTemplate: '',
    mappingMethod: '',
    executeMethod: '调度执行',
    creator: '',
    updateTime: '',
    lastExecuteRecord: '',
    executeStatus: '',
    description: '',
    status: '未生效',
    standardCatalog: 'data',
    standardName: '数据质量标准'
  },
  {
    id: 3,
    ruleName: '技术标准映射规则',
    mappingStandard: '标准集 / 标准 789',
    standardTemplate: '模板 BBB',
    mappingMethod: '按字段匹配',
    executeMethod: '自动执行',
    creator: '小李',
    updateTime: '2025-06-07 14:30:00',
    lastExecuteRecord: '2025-06-07 15:00:00',
    executeStatus: '成功',
    description: '技术标准相关映射规则',
    status: '生效',
    standardCatalog: 'tech',
    standardName: '技术架构标准'
  }
]





// ==================== 业务方法 ====================

/**
 * 处理表格行选择变化
 * @param {Array} selection 选中的行数据
 */
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
  console.log('选择变化:', selection)
}

/**
 * 处理搜索操作
 * 重置页码并重新获取数据
 */
const handleSearch = () => {
  queryParams.pageNum = 1
  getList()
}

/**
 * 处理重置操作
 * 清空搜索条件并重新获取数据
 */
const handleReset = () => {
  searchForm.ruleName = ''
  searchForm.standardCatalog = ''
  searchForm.standardName = ''
  queryParams.pageNum = 1
  getList()
}

/**
 * 获取规则列表数据
 * 根据搜索条件过滤数据并更新表格
 */
const getList = () => {
  // 模拟API调用，实际项目中应该调用后端接口
  let filteredData = mockData

  // 根据规则名称过滤数据
  if (searchForm.ruleName) {
    filteredData = filteredData.filter(item =>
      item.ruleName.includes(searchForm.ruleName)
    )
  }

  // 根据标准目录过滤数据
  if (searchForm.standardCatalog) {
    filteredData = filteredData.filter(item =>
      item.standardCatalog === searchForm.standardCatalog
    )
  }

  // 根据标准名称过滤数据
  if (searchForm.standardName) {
    filteredData = filteredData.filter(item =>
      item.standardName && item.standardName.includes(searchForm.standardName)
    )
  }

  total.value = filteredData.length
  tableData.value = filteredData
}

/**
 * 处理新建规则操作
 * 打开规则编辑抽屉，初始化为新建模式
 */
const handleCreate = () => {
  ruleDrawer.visible = true
  ruleDrawer.title = '新建映射规则'
  ruleDrawer.data = {}
}

/**
 * 处理查看规则操作
 * 打开规则编辑抽屉，设置为只读模式
 * @param {Object} row 规则行数据
 */
const handleView = (row) => {
  ruleDrawer.visible = true
  ruleDrawer.title = '查看映射规则'
  ruleDrawer.data = { ...row, readonly: true }
}

/**
 * 处理编辑规则操作
 * 打开规则编辑抽屉，加载现有规则数据
 * @param {Object} row 规则行数据
 */
const handleEdit = (row) => {
  ruleDrawer.visible = true
  ruleDrawer.title = '编辑映射规则'
  ruleDrawer.data = row
}

/**
 * 处理复制规则操作
 * @param {Object} row 规则行数据
 */
const handleCopy = (row) => {
  ElMessage.info(`复制规则: ${row.ruleName}`)
}

/**
 * 处理执行规则操作
 * @param {Object} row 规则行数据
 */
const handleExecute = (row) => {
  ElMessage.info(`执行规则: ${row.ruleName}`)
}

/**
 * 处理查看执行记录操作
 * 打开执行记录抽屉
 * @param {Object} row 规则行数据
 */
const handleExecuteRecord = (row) => {
  executeRecordDrawer.visible = true
  executeRecordDrawer.ruleName = row.ruleName
}

/**
 * 处理删除规则操作
 * 显示确认对话框，确认后删除规则
 * @param {Object} row 规则行数据
 */
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除规则"${row.ruleName}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('删除成功')
    getList()
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

/**
 * 处理批量删除规则操作
 * 显示确认对话框，确认后批量删除选中的规则
 */
const handleBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要删除的规则')
    return
  }

  const ruleNames = selectedRows.value.map(row => row.ruleName).join('、')
  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedRows.value.length} 条规则吗？\n规则名称：${ruleNames}`,
    '批量删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success(`成功删除 ${selectedRows.value.length} 条规则`)
    selectedRows.value = []
    getList()
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

/**
 * 处理下拉菜单命令
 * @param {String} command 命令类型
 * @param {Object} row 行数据
 */
const handleDropdownCommand = (command, row) => {
  switch (command) {
    case 'copy':
      handleCopy(row)
      break
    case 'executeRecord':
      handleExecuteRecord(row)
      break
    case 'delete':
      handleDelete(row)
      break
    default:
      console.warn('未知的命令:', command)
  }
}

/**
 * 查看落标明细
 * 打开明细弹窗显示执行记录详情
 * @param {Object} row 执行记录行数据
 */
const handleViewDetail = (row) => {
  detailDialog.visible = true
  detailDialog.data = row
}

/**
 * 查看执行日志
 * @param {Object} row 执行记录行数据
 */
const handleViewLog = (row) => {
  ElMessage.info(`查看日志: ${row.startTime}`)
}

/**
 * 保存规则
 * 处理规则保存成功后的操作
 */
const handleSaveRule = () => {
  ElMessage.success('保存成功')
  getList()
}

// ==================== 生命周期 ====================

/**
 * 组件挂载后初始化数据
 */
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
@import '@/assets/styles/xg-ui/base.scss';

.app-container {
  width: 100%;
  height: 100%;

  & > .el-row {
    height: 100%;
    .el-col {
      height: 100%;
    }
  }

  .rule-tabs {
    margin-bottom: 20px;

    :deep(.el-tabs__header) {
      margin-bottom: 0;
    }
  }

  .search-box {
    margin: 0;
    text-align: right;
    .el-form-item--default {
      margin-bottom: 20px;
      &:last-child {
        margin-right: 0;
      }
    }
  }

  .table-box {
    height: calc(100% - 250px);
  }

  .mb8 {
    margin-bottom: 20px;
  }
}

.text-muted {
  color: #909399;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;

  .primary-actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}
</style>